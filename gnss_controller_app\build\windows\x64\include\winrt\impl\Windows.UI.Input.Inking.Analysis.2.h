// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Input_Inking_Analysis_2_H
#define WINRT_Windows_UI_Input_Inking_Analysis_2_H
#include "winrt/impl/Windows.UI.Input.Inking.Analysis.1.h"
WINRT_EXPORT namespace winrt::Windows::UI::Input::Inking::Analysis
{
    struct __declspec(empty_bases) InkAnalysisInkBullet : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisInkBullet
    {
        InkAnalysisInkBullet(std::nullptr_t) noexcept {}
        InkAnalysisInkBullet(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisInkBullet(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) InkAnalysisInkDrawing : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisInkDrawing
    {
        InkAnalysisInkDrawing(std::nullptr_t) noexcept {}
        InkAnalysisInkDrawing(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisInkDrawing(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) InkAnalysisInkWord : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisInkWord
    {
        InkAnalysisInkWord(std::nullptr_t) noexcept {}
        InkAnalysisInkWord(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisInkWord(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) InkAnalysisLine : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisLine
    {
        InkAnalysisLine(std::nullptr_t) noexcept {}
        InkAnalysisLine(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisLine(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) InkAnalysisListItem : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisListItem
    {
        InkAnalysisListItem(std::nullptr_t) noexcept {}
        InkAnalysisListItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisListItem(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) InkAnalysisNode : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisNode
    {
        InkAnalysisNode(std::nullptr_t) noexcept {}
        InkAnalysisNode(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisNode(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) InkAnalysisParagraph : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisParagraph
    {
        InkAnalysisParagraph(std::nullptr_t) noexcept {}
        InkAnalysisParagraph(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisParagraph(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) InkAnalysisResult : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisResult
    {
        InkAnalysisResult(std::nullptr_t) noexcept {}
        InkAnalysisResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisResult(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) InkAnalysisRoot : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisRoot
    {
        InkAnalysisRoot(std::nullptr_t) noexcept {}
        InkAnalysisRoot(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisRoot(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) InkAnalysisWritingRegion : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisWritingRegion
    {
        InkAnalysisWritingRegion(std::nullptr_t) noexcept {}
        InkAnalysisWritingRegion(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalysisWritingRegion(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) InkAnalyzer : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalyzer
    {
        InkAnalyzer(std::nullptr_t) noexcept {}
        InkAnalyzer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Input::Inking::Analysis::IInkAnalyzer(ptr, take_ownership_from_abi) {}
        InkAnalyzer();
    };
}
#endif
