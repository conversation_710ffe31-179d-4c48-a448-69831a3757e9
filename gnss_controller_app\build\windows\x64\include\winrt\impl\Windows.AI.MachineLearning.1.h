// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_AI_MachineLearning_1_H
#define WINRT_Windows_AI_MachineLearning_1_H
#include "winrt/impl/Windows.AI.MachineLearning.0.h"
WINRT_EXPORT namespace winrt::Windows::AI::MachineLearning
{
    struct __declspec(empty_bases) IImageFeatureDescriptor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImageFeatureDescriptor>
    {
        IImageFeatureDescriptor(std::nullptr_t = nullptr) noexcept {}
        IImageFeatureDescriptor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImageFeatureDescriptor2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImageFeatureDescriptor2>
    {
        IImageFeatureDescriptor2(std::nullptr_t = nullptr) noexcept {}
        IImageFeatureDescriptor2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImageFeatureValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImageFeatureValue>
    {
        IImageFeatureValue(std::nullptr_t = nullptr) noexcept {}
        IImageFeatureValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImageFeatureValueStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImageFeatureValueStatics>
    {
        IImageFeatureValueStatics(std::nullptr_t = nullptr) noexcept {}
        IImageFeatureValueStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModel>
    {
        ILearningModel(std::nullptr_t = nullptr) noexcept {}
        ILearningModel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelBinding :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelBinding>
    {
        ILearningModelBinding(std::nullptr_t = nullptr) noexcept {}
        ILearningModelBinding(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelBindingFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelBindingFactory>
    {
        ILearningModelBindingFactory(std::nullptr_t = nullptr) noexcept {}
        ILearningModelBindingFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelDevice>
    {
        ILearningModelDevice(std::nullptr_t = nullptr) noexcept {}
        ILearningModelDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelDeviceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelDeviceFactory>
    {
        ILearningModelDeviceFactory(std::nullptr_t = nullptr) noexcept {}
        ILearningModelDeviceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelDeviceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelDeviceStatics>
    {
        ILearningModelDeviceStatics(std::nullptr_t = nullptr) noexcept {}
        ILearningModelDeviceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelEvaluationResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelEvaluationResult>
    {
        ILearningModelEvaluationResult(std::nullptr_t = nullptr) noexcept {}
        ILearningModelEvaluationResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelFeatureDescriptor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelFeatureDescriptor>
    {
        ILearningModelFeatureDescriptor(std::nullptr_t = nullptr) noexcept {}
        ILearningModelFeatureDescriptor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelFeatureValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelFeatureValue>
    {
        ILearningModelFeatureValue(std::nullptr_t = nullptr) noexcept {}
        ILearningModelFeatureValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelOperatorProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelOperatorProvider>
    {
        ILearningModelOperatorProvider(std::nullptr_t = nullptr) noexcept {}
        ILearningModelOperatorProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelSession :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelSession>
    {
        ILearningModelSession(std::nullptr_t = nullptr) noexcept {}
        ILearningModelSession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelSessionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelSessionFactory>
    {
        ILearningModelSessionFactory(std::nullptr_t = nullptr) noexcept {}
        ILearningModelSessionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelSessionFactory2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelSessionFactory2>
    {
        ILearningModelSessionFactory2(std::nullptr_t = nullptr) noexcept {}
        ILearningModelSessionFactory2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelSessionOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelSessionOptions>
    {
        ILearningModelSessionOptions(std::nullptr_t = nullptr) noexcept {}
        ILearningModelSessionOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelSessionOptions2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelSessionOptions2>
    {
        ILearningModelSessionOptions2(std::nullptr_t = nullptr) noexcept {}
        ILearningModelSessionOptions2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelSessionOptions3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelSessionOptions3>
    {
        ILearningModelSessionOptions3(std::nullptr_t = nullptr) noexcept {}
        ILearningModelSessionOptions3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILearningModelStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILearningModelStatics>
    {
        ILearningModelStatics(std::nullptr_t = nullptr) noexcept {}
        ILearningModelStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapFeatureDescriptor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapFeatureDescriptor>
    {
        IMapFeatureDescriptor(std::nullptr_t = nullptr) noexcept {}
        IMapFeatureDescriptor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISequenceFeatureDescriptor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISequenceFeatureDescriptor>
    {
        ISequenceFeatureDescriptor(std::nullptr_t = nullptr) noexcept {}
        ISequenceFeatureDescriptor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensor>,
        impl::require<winrt::Windows::AI::MachineLearning::ITensor, winrt::Windows::AI::MachineLearning::ILearningModelFeatureValue>
    {
        ITensor(std::nullptr_t = nullptr) noexcept {}
        ITensor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorBoolean :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorBoolean>
    {
        ITensorBoolean(std::nullptr_t = nullptr) noexcept {}
        ITensorBoolean(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorBooleanStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorBooleanStatics>
    {
        ITensorBooleanStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorBooleanStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorBooleanStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorBooleanStatics2>
    {
        ITensorBooleanStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorBooleanStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorDouble :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorDouble>
    {
        ITensorDouble(std::nullptr_t = nullptr) noexcept {}
        ITensorDouble(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorDoubleStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorDoubleStatics>
    {
        ITensorDoubleStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorDoubleStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorDoubleStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorDoubleStatics2>
    {
        ITensorDoubleStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorDoubleStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorFeatureDescriptor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorFeatureDescriptor>
    {
        ITensorFeatureDescriptor(std::nullptr_t = nullptr) noexcept {}
        ITensorFeatureDescriptor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorFloat :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorFloat>
    {
        ITensorFloat(std::nullptr_t = nullptr) noexcept {}
        ITensorFloat(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorFloat16Bit :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorFloat16Bit>
    {
        ITensorFloat16Bit(std::nullptr_t = nullptr) noexcept {}
        ITensorFloat16Bit(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorFloat16BitStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorFloat16BitStatics>
    {
        ITensorFloat16BitStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorFloat16BitStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorFloat16BitStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorFloat16BitStatics2>
    {
        ITensorFloat16BitStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorFloat16BitStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorFloatStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorFloatStatics>
    {
        ITensorFloatStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorFloatStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorFloatStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorFloatStatics2>
    {
        ITensorFloatStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorFloatStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt16Bit :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt16Bit>
    {
        ITensorInt16Bit(std::nullptr_t = nullptr) noexcept {}
        ITensorInt16Bit(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt16BitStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt16BitStatics>
    {
        ITensorInt16BitStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorInt16BitStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt16BitStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt16BitStatics2>
    {
        ITensorInt16BitStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorInt16BitStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt32Bit :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt32Bit>
    {
        ITensorInt32Bit(std::nullptr_t = nullptr) noexcept {}
        ITensorInt32Bit(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt32BitStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt32BitStatics>
    {
        ITensorInt32BitStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorInt32BitStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt32BitStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt32BitStatics2>
    {
        ITensorInt32BitStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorInt32BitStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt64Bit :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt64Bit>
    {
        ITensorInt64Bit(std::nullptr_t = nullptr) noexcept {}
        ITensorInt64Bit(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt64BitStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt64BitStatics>
    {
        ITensorInt64BitStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorInt64BitStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt64BitStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt64BitStatics2>
    {
        ITensorInt64BitStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorInt64BitStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt8Bit :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt8Bit>
    {
        ITensorInt8Bit(std::nullptr_t = nullptr) noexcept {}
        ITensorInt8Bit(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt8BitStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt8BitStatics>
    {
        ITensorInt8BitStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorInt8BitStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorInt8BitStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorInt8BitStatics2>
    {
        ITensorInt8BitStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorInt8BitStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorString :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorString>
    {
        ITensorString(std::nullptr_t = nullptr) noexcept {}
        ITensorString(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorStringStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorStringStatics>
    {
        ITensorStringStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorStringStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorStringStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorStringStatics2>
    {
        ITensorStringStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorStringStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt16Bit :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt16Bit>
    {
        ITensorUInt16Bit(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt16Bit(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt16BitStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt16BitStatics>
    {
        ITensorUInt16BitStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt16BitStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt16BitStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt16BitStatics2>
    {
        ITensorUInt16BitStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt16BitStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt32Bit :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt32Bit>
    {
        ITensorUInt32Bit(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt32Bit(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt32BitStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt32BitStatics>
    {
        ITensorUInt32BitStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt32BitStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt32BitStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt32BitStatics2>
    {
        ITensorUInt32BitStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt32BitStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt64Bit :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt64Bit>
    {
        ITensorUInt64Bit(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt64Bit(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt64BitStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt64BitStatics>
    {
        ITensorUInt64BitStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt64BitStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt64BitStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt64BitStatics2>
    {
        ITensorUInt64BitStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt64BitStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt8Bit :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt8Bit>
    {
        ITensorUInt8Bit(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt8Bit(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt8BitStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt8BitStatics>
    {
        ITensorUInt8BitStatics(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt8BitStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITensorUInt8BitStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITensorUInt8BitStatics2>
    {
        ITensorUInt8BitStatics2(std::nullptr_t = nullptr) noexcept {}
        ITensorUInt8BitStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
