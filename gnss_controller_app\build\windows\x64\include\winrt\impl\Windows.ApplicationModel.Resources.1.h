// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Resources_1_H
#define WINRT_Windows_ApplicationModel_Resources_1_H
#include "winrt/impl/Windows.ApplicationModel.Resources.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Resources
{
    struct __declspec(empty_bases) IResourceLoader :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceLoader>
    {
        IResourceLoader(std::nullptr_t = nullptr) noexcept {}
        IResourceLoader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IResourceLoader2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceLoader2>
    {
        IResourceLoader2(std::nullptr_t = nullptr) noexcept {}
        IResourceLoader2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IResourceLoaderFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceLoaderFactory>
    {
        IResourceLoaderFactory(std::nullptr_t = nullptr) noexcept {}
        IResourceLoaderFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IResourceLoaderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceLoaderStatics>
    {
        IResourceLoaderStatics(std::nullptr_t = nullptr) noexcept {}
        IResourceLoaderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IResourceLoaderStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceLoaderStatics2>
    {
        IResourceLoaderStatics2(std::nullptr_t = nullptr) noexcept {}
        IResourceLoaderStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IResourceLoaderStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceLoaderStatics3>
    {
        IResourceLoaderStatics3(std::nullptr_t = nullptr) noexcept {}
        IResourceLoaderStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IResourceLoaderStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IResourceLoaderStatics4>
    {
        IResourceLoaderStatics4(std::nullptr_t = nullptr) noexcept {}
        IResourceLoaderStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
